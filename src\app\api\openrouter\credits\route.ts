import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { apiKey } = await request.json();

    if (!apiKey) {
      return NextResponse.json(
        { error: 'API key é obrigatória' },
        { status: 400 }
      );
    }

    // Fazer requisição para a API da OpenRouter
    const response = await fetch('https://openrouter.ai/api/v1/credits', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Erro da API OpenRouter: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const { total_credits, total_usage } = data.data;
    const balance = total_credits - total_usage;

    return NextResponse.json({
      success: true,
      balance,
      total_credits,
      total_usage,
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });

  } catch (error) {
    console.error('Erro ao buscar créditos da OpenRouter:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Erro ao buscar créditos da OpenRouter',
      details: error instanceof Error ? error.message : 'Erro desconhecido',
    }, { 
      status: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  }
}

// OPTIONS - Para CORS
export async function OPTIONS() {
  return NextResponse.json({}, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
